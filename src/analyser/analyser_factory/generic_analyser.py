import traceback
import uuid
from pathlib import Path
from typing import Any, Optional, List, Dict

from utils.files import Files
from utils.logger import Logger
from utils.logs import Logs
from utils.time_monitor import TimeMonitor


class GenericAnalyser:
    """
    Generic Analyser class to be inherited by specific analysers
    """

    NAME = "GenericAnalyser"
    ANALYSIS_METHOD = ""
    VOCABULARY_PATH = Path(__file__).parent.parent.parent / "vocabularies"
    ERRORS = ""
    FALSE_POSITIVES = ""
    SUCCESS = ""
    HINTS = ""
    LOG_LEVEL = Logger.INFO

    def __init__(
        self,
        input: str,
        match_at: float,
        verbose: bool,
        output: Optional[str],
        prompt_output: Optional[str],
        training_mode: bool,
        ground_truth_folder: Optional[str],
        rag: Optional[str],
        logger: Optional[Logger] = None,
        ini_config: Optional[str] = None,
        streamer: Optional[Any] = None,
        cfg: Optional[Any] = None,
        error_vocab: Optional[str] = None,
        false_positive_vocab: Optional[str] = None,
        hint_vocab: Optional[str] = None,
        success_vocab: Optional[str] = None,
    ) -> None:
        """
        Initializes the object with the provided arguments and logger.
        Args: input (str): Path to the input file, match_at (int): Matching threshold, last_rows (int): Number of last rows to consider, verbose (bool): Verbose mode, output (str): Output path, prompt_output (str): Prompt output, training_mode (bool): Training mode flag, ground_truth_folder (str): Ground truth folder path, rag (str): RAG parameter, logger (Logger): Logger instance, ini_config (str): INI configuration, streamer (str): Streamer instance.
        Returns: None
        """
        self.logger = (
            logger
            if logger
            else Logger(
                name=f"Analyser-{uuid.uuid4().hex}",
                logfile="analyser.log",
                level=self.LOG_LEVEL,
            )
        )
        self.input = input
        self.match_at = match_at
        self.verbose = verbose
        self.output = output
        self.prompt_output = prompt_output
        self.training_mode = training_mode
        self.ground_truth_folder = ground_truth_folder
        self.rag = rag
        self.lgu = Logs(self.logger)
        self.files = Files(self.logger)
        self.log_file_path = Path(input)
        self.log_lines = None
        self.errors = None
        self.fs_vocabulary = None
        self.success = None
        self.hints = None
        self.cfg = cfg

        self.ERRORS = error_vocab if error_vocab else self.ERRORS
        self.FALSE_POSITIVES = false_positive_vocab if false_positive_vocab else self.FALSE_POSITIVES
        self.SUCCESS = success_vocab if success_vocab else self.SUCCESS
        self.HINTS = hint_vocab if hint_vocab else self.HINTS

    def get_vocabularies(self) -> None:
        try:
            self.errors = set(self.files.get_json(str(self.VOCABULARY_PATH / self.ERRORS))) if self.ERRORS else set()
            self.fs_vocabulary = set(self.files.get_json(str(self.VOCABULARY_PATH / self.FALSE_POSITIVES))) if self.FALSE_POSITIVES else set()
            self.success = set(self.files.get_json(str(self.VOCABULARY_PATH / self.SUCCESS))) if self.SUCCESS else set()
            self.hints = set(self.files.get_json(str(self.VOCABULARY_PATH / self.HINTS))) if self.HINTS else set()
        except Exception as e:
            self.logger.error(f"{self.NAME}: {e}, {traceback.format_exc()}")
            raise e

    def get_log_lines(self) -> None:
        """
        Retrieves the lines from the log file.
        Args: None
        Returns: list: A list of log lines.
        """
        try:
            self.log_lines = self.files.get_lines_iter(str(self.log_file_path), ignore_errors=True)
        except Exception as e:
            self.logger.error(f"{self.NAME}: {e}, {traceback.format_exc()}")
            raise e

    def is_false_positive(self, line: str) -> bool:
        """
        Check if the given line is a false positive.

        Parameters:
        line (str): The line to be checked for false positives.

        Returns:
        bool: True if the line is a false positive, False otherwise.
        """
        if self.fs_vocabulary:
            return any(keyword in line for keyword in self.fs_vocabulary)
        return False

    def get_score(self, line: str, keyword: str) -> float:
        """
        Either found or not found, but we want to keep to float scores not to potential booleans.

        Args:
            line (str): The line to be analyzed.
            keyword (str): The keyword to be found.

        Returns:
            float: The score of the keyword in the line.
        """
        if keyword in line and not self.is_false_positive(line):
            return 0.999
        return 0.001

    def get_match(self, line_number: int, line: str, pattern: str, dict_section: str = "errors") -> Optional[Dict]:
        """
        We use the keyword matching algorithm, which is very fast and efficient, but not very accurate.

        Args:
            line_number (int): The line number of the line being matched.
            line (str): The content of the line being matched.
            pattern (str): The pattern to be found.
            dict_section (str): The dict_section inside a json dictionary file to be used for the match

        Returns:
            dict: The result of the match.
        """
        score = self.get_score(line, pattern.lower())
        if score >= self.match_at:
            return self.get_result(line_number, line, pattern.lower(), score, self.ANALYSIS_METHOD)
        return None

    def get_match_line(self, i: int, line: str, vocabulary: Any, result: List) -> List:
        line = self.lgu.filter_time_stamp(line)
        line = self.lgu.filter_trash_line(line, stem=False).lower()
        for match in vocabulary:
            data = self.get_match(i, line, match)
            if data and data.get("score") is not None and isinstance(data.get("score"), (int, float)) and data.get("score") >= self.match_at:
                result.append(data.get("line"))
                break  # Only first valid match per line
        return result

    def analyze_log_lines(self) -> Any:
        """
        Analyzes the log lines using the get_match() function and returns the result

        Parameters:
        log_file_lines : List[str]
            The list of log file lines to be analyzed

        Returns:
        list
            The list containing the results of the analysis
        """
        errors, hints, success = [], [], []
        self.logger.debug(f"{self.NAME}: Performing analysis...")
        count = 0
        if self.log_lines is None:
            self.logger.error(f"{self.NAME}: log_lines is None, cannot analyze.")
            return []
        for i, line in enumerate(self.log_lines, start=1):
            if not line.strip():
                continue
            # Priority: error > hint > success
            matched = False
            for match in self.errors:
                data = self.get_match(i, line, match)
                score = data.get("score") if data else None
                if data and score is not None and isinstance(score, (int, float)) and score >= self.match_at:
                    errors.append(data.get("line"))
                    matched = True
                    break
            if matched:
                count += 1
                continue
            for match in self.hints:
                data = self.get_match(i, line, match)
                score = data.get("score") if data else None
                if data and score is not None and isinstance(score, (int, float)) and score >= self.match_at:
                    hints.append(data.get("line"))
                    matched = True
                    break
            if matched:
                count += 1
                continue
            for match in self.success:
                data = self.get_match(i, line, match)
                score = data.get("score") if data else None
                if data and score is not None and isinstance(score, (int, float)) and score >= self.match_at:
                    success.append(data.get("line"))
                    break
            count += 1
        # Deduplicate results
        errors = list(dict.fromkeys(errors))
        hints = list(dict.fromkeys(hints))
        success = list(dict.fromkeys(success))
        self.logger.debug(f"{self.NAME}: Done analyzing, {count} lines processed.")
        return errors, hints, success

    @staticmethod
    def get_result(
        line_number: int,
        line: str,
        match: str,
        score: float,
        method: str,
        type: Optional[str] = None,
    ) -> Dict:
        """
        Returns a vocabulary containing the result of a log analysis.
        Args: line_number (str): The line number of the log entry, line (str): The content of the log entry, match (bool): Indicates whether the log entry has a match or not, score (bool): The score of the log entry, method (str): The method used for log analysis, type (str, optional): The type of the log entry. Defaults to None.
        Returns: dict: A vocabulary containing the result of a log analysis.
        """
        result = {
            "line": line_number,
            "log": line,
            "match": match,
            "method": method,
            "score": score,
        }
        if type:
            result["type"] = type
        return result

    def analyze(self) -> Dict:
        """
        Analyzes logs and returns the result of analysis.
        Args: None
        Returns: dict: The resulting analysis is returned.
        """
        try:
            with TimeMonitor(f"{self.NAME}: -- Analysis completed in: ", self.logger):
                self.logger.debug(f"{self.NAME}: Loading vocabularies ...")
                self.get_vocabularies()
                self.logger.debug(f"{self.NAME}: Loading log file ...")
                self.get_log_lines()
                self.logger.debug(f"{self.NAME}: Analyzing logs ...")
                errors, hints, success = self.analyze_log_lines()
                result = self.lgu.format_result(errors, hints, success, str(self.log_file_path), None)
                return result
        except Exception as e:
            self.logger.error(f"{self.NAME}: {e}, {traceback.format_exc()}")
            raise e
