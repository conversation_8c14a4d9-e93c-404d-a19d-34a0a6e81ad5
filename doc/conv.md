Thank you for the detailed context! Let me structure your requirements and suggest a plan:

## Current State Analysis
- **Existing**: File-based analysis storage with token auth and user context
- **Missing**: User persistence, project management, proper access control
- **Goal**: Add lightweight user/project management without over-engineering

## Requirements Summary

### Users
- **No registration flow** - users come from:
  - Company directory/AD groups
  - Manual additions to local DB
- **Storage**: TinyDB for local user data (roles, preferences, etc.)
- **Auth**: Keep existing token system

### Projects
- **Simple entities**: ID, name, description
- **Access control**: Via AD groups (future) + manual assignments
- **Qdrant integration**: Projects ↔ collections (naming convention based)
- **Scale**: Hundreds of projects, thousands of users

### Analysis Organization
- **Current**: Flat structure in `analysis_store/`
- **Needed**: Organization by user/project context
- **Persistence**: Some analyses are temporary, others permanent

## Suggested Plan

### Phase 1: Basic TinyDB Structure
1. **User DB**: Store user metadata, roles, manual users
2. **Project DB**: Basic project info and access lists
3. **Analysis DB**: Link analyses to users/projects
4. **Keep existing**: File-based analysis storage, just organize better

### Phase 2: Directory Organization
1. **Restructure**: `analysis_store/users/{user_id}/` and `analysis_store/projects/{project_id}/`
2. **Backward compatibility**: Migration for existing analyses
3. **Access verification**: Enhanced with project context

### Phase 3: Frontend Management
1. **User management**: Simple CRUD for maintainers
2. **Project management**: Create/assign projects
3. **Analysis browser**: View by user/project context

## Questions for Refinement

1. **User identification**: What's the unique identifier from your directory system? (username, email, employee ID?)

2. **Project access**: Should we start with simple user lists per project, or wait for AD group integration?

3. **Analysis organization**: Should analyses belong to either a user OR a project, or can they be personal analyses within a project context?

4. **Migration priority**: Do you need to handle existing analyses in `analysis_store/` or start fresh?

5. **Maintainer capabilities**: What level of user/project management should maintainers have vs admins?

This approach keeps it minimal - just adding the organizational layer you need without heavy authentication or complex relationships. Should I proceed with a specific part, or would you like to refine the requirements first?